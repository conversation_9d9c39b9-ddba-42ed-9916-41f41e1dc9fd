import React, { useState, useRef, useCallback, useEffect } from "react";
import { IconButton } from "./button";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import styles from "./object-replace.module.scss";

import UploadIcon from "../icons/upload.svg";
import BackIcon from "../icons/back.svg";
import CloseIcon from "../icons/close.svg";

// 导入API和工具函数
import {
  uploadImageMultipart,
  fluxAnythingReplace,
  ImageType as ApiImageType,
  FluxAnythingReplaceRequest,
  HandleType,
} from "../api/custom-api/ai-tool-api";

// 导入共享的遮罩工具
import {
  base64ToFile,
  createBlackWhiteMask,
  createAlphaMaskImage,
  createMaskImage,
  setupDrawingTool,
  drawDot,
  safeToDataURL,
} from "../utils/mask-utils";
import {
  compressImage,
  IMAGE_COMPRESSION_PRESETS,
} from "../utils/image-compression-lib";

// 类型定义
type ImageType = "source" | "target";
type TabType = "immediate" | "result";
type ToolType = "brush" | "eraser";

interface Point {
  x: number;
  y: number;
}

interface Material {
  id: number;
  image: string;
  name: string;
  type: ImageType;
}

// 常量定义
const DEFAULT_BRUSH_SIZE = 32;
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
  "image/bmp",
  "image/tiff",
  "image/tif",
  "image/svg+xml",
  "image/avif",
  "image/heic",
  "image/heif",
  "image/ico",
  "image/x-icon",
  "image/vnd.microsoft.icon",
];
const PROCESSING_TIMEOUT = 30000; // 30秒

// 导入配置文件
import {
  OBJECT_REPLACE_EXAMPLES,
  MATERIAL_IMAGES,
  getObjectReplaceExamplePaths,
  preloadImages,
} from "../config/images";

// 将配置文件中的素材转换为组件需要的格式
const MATERIALS: Material[] = MATERIAL_IMAGES.map((material, index) => ({
  id: material.id,
  image: material.path,
  name: `素材${material.id}`, // 生成简单的名称
  type: index < 2 ? "source" : "target", // 前两个作为source，后两个作为target
}));

export function ObjectReplace() {
  const navigate = useNavigate();

  // 主要状态
  const [sourceImage, setSourceImage] = useState<string | null>(null);
  const [targetImage, setTargetImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>("immediate");
  const [error, setError] = useState<string | null>(null);

  // 任务处理相关状态
  const [taskId, setTaskId] = useState<string | null>(null);
  const [taskStatus, setTaskStatus] = useState<
    "waiting" | "processing" | "completed" | "failed" | null
  >(null);
  const [originalImageFile, setOriginalImageFile] = useState<File | null>(null);
  const [replaceImageFile, setReplaceImageFile] = useState<File | null>(null);

  // 重绘相关状态 (参考图片编辑页面)
  const [maskCanvas, setMaskCanvas] = useState<string | null>(null);
  const [showInpaintModal, setShowInpaintModal] = useState(false);
  const [currentEditingImage, setCurrentEditingImage] =
    useState<ImageType | null>(null);
  const [brushSize, setBrushSize] = useState(DEFAULT_BRUSH_SIZE);
  const [isDrawing, setIsDrawing] = useState(false);
  const [maskHistory, setMaskHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [currentTool, setCurrentTool] = useState<ToolType>("brush");
  const [lastPoint, setLastPoint] = useState<Point | null>(null);

  // Refs
  const sourceFileInputRef = useRef<HTMLInputElement>(null);
  const targetFileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // 工具函数
  const validateFile = useCallback((file: File): string | null => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return `不支持的文件类型。请选择 ${ALLOWED_FILE_TYPES.join(
        ", ",
      )} 格式的图片。`;
    }
    if (file.size > MAX_FILE_SIZE) {
      return `文件大小超过限制。请选择小于 ${
        MAX_FILE_SIZE / 1024 / 1024
      }MB 的图片。`;
    }
    return null;
  }, []);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const showError = useCallback((message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000); // 5秒后自动清除错误
  }, []);

  // 坐标转换函数
  const getCanvasCoordinates = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = canvasRef.current;
      if (!canvas) return { x: 0, y: 0 };

      const rect = canvas.getBoundingClientRect();
      const scaleX = canvas.width / rect.width;
      const scaleY = canvas.height / rect.height;

      const x = (e.clientX - rect.left) * scaleX;
      const y = (e.clientY - rect.top) * scaleY;

      return { x, y };
    },
    [],
  );

  // 使用共享的遮罩工具（已移除重复实现）

  // 准备文件的辅助函数
  const prepareFiles = useCallback(async () => {
    let originalFile: File;
    let maskFile: File;
    let replaceFile: File;

    // 准备原图文件
    if (originalImageFile) {
      originalFile = originalImageFile;
    } else {
      originalFile = base64ToFile(sourceImage!, "original.png");
    }

    // 准备遮罩文件 (从重绘画布生成)
    if (maskCanvas) {
      maskFile = base64ToFile(maskCanvas, "mask.png");
    } else {
      throw new Error("请先对原图进行重绘标记需要替换的区域");
    }

    // 准备替换文件
    if (replaceImageFile) {
      replaceFile = replaceImageFile;
    } else {
      replaceFile = base64ToFile(targetImage!, "replace.png");
    }

    return { originalFile, maskFile, replaceFile };
  }, [
    originalImageFile,
    sourceImage,
    maskCanvas,
    replaceImageFile,
    targetImage,
    base64ToFile,
  ]);

  // 并发压缩和上传图片的辅助函数
  const compressAndUploadImages = useCallback(
    async (originalFile: File, maskFile: File, replaceFile: File) => {
      // 并发压缩图片
      const compressionTasks = [
        compressImage(originalFile, IMAGE_COMPRESSION_PRESETS.STANDARD),
        compressImage(maskFile, IMAGE_COMPRESSION_PRESETS.STANDARD),
        compressImage(replaceFile, IMAGE_COMPRESSION_PRESETS.STANDARD),
      ];

      const compressionResults = await Promise.all(compressionTasks);
      const compressedOriginal = compressionResults[0];
      const compressedMask = compressionResults[1];
      const compressedReplace = compressionResults[2];

      // 并发上传图片
      const uploadTasks = [
        uploadImageMultipart({
          uploadImage: compressedOriginal.file,
          imageType: ApiImageType.ORIGINAL,
          imageFormat: compressedOriginal.file.type.split("/")[1] || "png",
        }),
        uploadImageMultipart({
          uploadImage: compressedMask.file,
          imageType: ApiImageType.MASK,
          imageFormat: compressedMask.file.type.split("/")[1] || "png",
        }),
        uploadImageMultipart({
          uploadImage: compressedReplace.file,
          imageType: ApiImageType.ORIGINAL, // 替换图也使用ORIGINAL类型
          imageFormat: compressedReplace.file.type.split("/")[1] || "png",
        }),
      ];

      const uploadResults = await Promise.all(uploadTasks);
      return {
        originalResult: uploadResults[0],
        maskResult: uploadResults[1],
        replaceResult: uploadResults[2],
      };
    },
    [],
  );

  // 提交万物替换任务的辅助函数
  const submitReplaceTask = useCallback(
    async (uploadResults: {
      originalResult: any;
      maskResult: any;
      replaceResult: any;
    }) => {
      const replaceRequest: FluxAnythingReplaceRequest = {
        originalImageName: uploadResults.originalResult.name,
        maskImageName: uploadResults.maskResult.name,
        replaceImageName: uploadResults.replaceResult.name,
        handleType: HandleType.ANYTHING_REPLACE,
      };

      console.log("replaceRequest:", replaceRequest);

      return await fluxAnythingReplace(replaceRequest);
    },
    [],
  );

  // 预加载万物替换案例图片
  useEffect(() => {
    const imagePaths = getObjectReplaceExamplePaths();
    preloadImages(imagePaths).catch((error) => {
      console.warn("Failed to preload object replace example images:", error);
    });
  }, []);

  const handleImageUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, type: ImageType) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // 验证文件
      const validationError = validateFile(file);
      if (validationError) {
        showError(validationError);
        return;
      }

      resetError();

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const imageUrl = e.target?.result as string;
          if (!imageUrl) {
            throw new Error("无法读取图片文件");
          }

          if (type === "source") {
            setSourceImage(imageUrl);
            setOriginalImageFile(file); // 保存原始文件
            // 清理源图片相关的编辑状态
            if (currentEditingImage === "source") {
              setShowInpaintModal(false);
              setCurrentEditingImage(null);
            }
          } else {
            setTargetImage(imageUrl);
            setReplaceImageFile(file); // 保存替换文件
            // 清理目标图片相关的编辑状态
            if (currentEditingImage === "target") {
              setShowInpaintModal(false);
              setCurrentEditingImage(null);
            }
          }

          // 重置绘制状态
          setIsDrawing(false);
          setLastPoint(null);
          setMaskHistory([]);
          setHistoryIndex(-1);
        } catch (err) {
          showError("图片加载失败，请重试");
          console.error("Image upload error:", err);
        }
      };

      reader.onerror = () => {
        showError("文件读取失败，请重试");
      };

      reader.readAsDataURL(file);
    },
    [validateFile, showError, resetError],
  );

  const handleSourceUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      handleImageUpload(event, "source");
    },
    [handleImageUpload],
  );

  const handleTargetUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      handleImageUpload(event, "target");
    },
    [handleImageUpload],
  );

  // 删除图片
  const removeImage = useCallback(
    (type: ImageType) => {
      try {
        resetError();

        if (type === "source") {
          setSourceImage(null);
          setOriginalImageFile(null);
          // 清理重绘相关状态
          setMaskCanvas(null);
          if (sourceFileInputRef.current) {
            sourceFileInputRef.current.value = "";
          }
        } else {
          setTargetImage(null);
          setReplaceImageFile(null);
          if (targetFileInputRef.current) {
            targetFileInputRef.current.value = "";
          }
        }

        // 重置任务状态
        setTaskId(null);
        setTaskStatus(null);
      } catch (err) {
        console.error("Remove image error:", err);
        showError("删除图片时出错");
      }
    },
    [resetError, showError],
  );

  // 打开局部重绘弹框
  const handleOpenInpaint = useCallback(() => {
    try {
      resetError();

      if (!sourceImage) {
        showError("请先上传原图");
        return;
      }

      // 重置所有绘制相关状态
      setIsDrawing(false);
      setLastPoint(null);
      setMaskHistory([]);
      setHistoryIndex(-1);
      setBrushSize(DEFAULT_BRUSH_SIZE);
      setCurrentTool("brush");

      setCurrentEditingImage("source");
      setShowInpaintModal(true);
    } catch (err) {
      console.error("Open inpaint modal error:", err);
      showError("打开编辑器失败");
    }
  }, [resetError, showError, sourceImage]);

  // 关闭局部重绘弹框
  const handleCloseInpaint = useCallback(() => {
    try {
      setShowInpaintModal(false);
      setCurrentEditingImage(null);
      // 清理绘制状态
      setIsDrawing(false);
      setLastPoint(null);
    } catch (err) {
      console.error("Close inpaint modal error:", err);
      showError("关闭编辑器时出错");
    }
  }, [showError]);

  // 确认局部重绘
  const handleConfirmInpaint = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      const image = imageRef.current;

      if (!canvas || !image || !currentEditingImage) {
        console.error("Canvas, image or currentEditingImage not found");
        showError("编辑器状态异常，请重新打开");
        return;
      }

      // 验证画布和图片尺寸
      if (canvas.width === 0 || canvas.height === 0) {
        console.error("Canvas has invalid dimensions");
        showError("画布尺寸异常，请重新打开编辑器");
        return;
      }

      // 创建一个新的canvas来合成最终图片（显示用）
      const finalCanvas = document.createElement("canvas");
      const finalCtx = finalCanvas.getContext("2d");

      if (!finalCtx) {
        console.error("Failed to get canvas context");
        showError("无法创建画布上下文");
        return;
      }

      // 设置最终canvas的尺寸
      const imageWidth = image.naturalWidth || image.width;
      const imageHeight = image.naturalHeight || image.height;

      if (imageWidth === 0 || imageHeight === 0) {
        console.error("Image has invalid dimensions");
        showError("图片尺寸异常");
        return;
      }

      finalCanvas.width = imageWidth;
      finalCanvas.height = imageHeight;

      // 参考iOS的previewFromOriginal方法实现预览图生成（与图片编辑页面保持一致）
      // 1. 先绘制原始图片
      finalCtx.drawImage(image, 0, 0);

      // 2. 创建黑白遮罩并应用到预览图
      const blackWhiteMask = createBlackWhiteMask(canvas);

      // 3. 使用destination-out清除涂抹区域（白色区域）
      finalCtx.globalCompositeOperation = "destination-out";

      // 创建临时画布，只保留白色区域用于清除
      const tempCanvas = document.createElement("canvas");
      const tempCtx = tempCanvas.getContext("2d");
      if (tempCtx) {
        tempCanvas.width = blackWhiteMask.width;
        tempCanvas.height = blackWhiteMask.height;

        // 绘制黑白遮罩
        tempCtx.drawImage(blackWhiteMask, 0, 0);

        // 只保留白色区域（涂抹区域）
        const imageData = tempCtx.getImageData(
          0,
          0,
          tempCanvas.width,
          tempCanvas.height,
        );
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          if (data[i] > 128) {
            // 白色像素（涂抹区域）
            data[i] = 255; // R
            data[i + 1] = 255; // G
            data[i + 2] = 255; // B
            data[i + 3] = 255; // A
          } else {
            data[i] = 0; // R
            data[i + 1] = 0; // G
            data[i + 2] = 0; // B
            data[i + 3] = 0; // A (透明)
          }
        }

        tempCtx.putImageData(imageData, 0, 0);
        finalCtx.drawImage(tempCanvas, 0, 0);
      }

      // 4. 保存纯遮罩用于API调用（黑白遮罩转换为黑色遮罩）
      try {
        // 创建黑色遮罩（涂抹区域为黑色，未涂抹区域为透明）
        const blackMaskCanvas = document.createElement("canvas");
        const blackMaskCtx = blackMaskCanvas.getContext("2d");

        if (blackMaskCtx) {
          blackMaskCanvas.width = blackWhiteMask.width;
          blackMaskCanvas.height = blackWhiteMask.height;

          // 获取黑白遮罩数据
          const maskImageData = blackWhiteMask
            .getContext("2d")
            ?.getImageData(0, 0, blackWhiteMask.width, blackWhiteMask.height);
          if (maskImageData) {
            const blackMaskData = blackMaskCtx.createImageData(
              blackWhiteMask.width,
              blackWhiteMask.height,
            );
            const srcData = maskImageData.data;
            const blackData = blackMaskData.data;

            for (let i = 0; i < srcData.length; i += 4) {
              if (srcData[i] > 128) {
                // 白色像素（涂抹区域）
                // 转换为黑色
                blackData[i] = 0; // R = 0 (黑色)
                blackData[i + 1] = 0; // G = 0 (黑色)
                blackData[i + 2] = 0; // B = 0 (黑色)
                blackData[i + 3] = 255; // A = 255 (完全不透明)
              } else {
                // 未涂抹区域设为透明
                blackData[i] = 0; // R = 0
                blackData[i + 1] = 0; // G = 0
                blackData[i + 2] = 0; // B = 0
                blackData[i + 3] = 0; // A = 0 (透明)
              }
            }

            blackMaskCtx.putImageData(blackMaskData, 0, 0);

            const maskDataUrl = blackMaskCanvas.toDataURL("image/png");
            if (
              maskDataUrl &&
              maskDataUrl.startsWith("data:") &&
              maskDataUrl.includes(",")
            ) {
              setMaskCanvas(maskDataUrl);
            } else {
              console.error("Invalid mask data URL generated:", maskDataUrl);
              setMaskCanvas(null);
            }
          }
        }
      } catch (error) {
        console.error("Error generating mask data URL:", error);
        setMaskCanvas(null);
      }

      // 将合成后的图片转换为base64并更新显示的图片
      let finalImageData: string;
      try {
        finalImageData = finalCanvas.toDataURL("image/png");
        // 验证生成的data URL
        if (
          !finalImageData ||
          !finalImageData.startsWith("data:") ||
          !finalImageData.includes(",")
        ) {
          throw new Error("Invalid data URL generated");
        }
      } catch (securityError) {
        console.warn(
          "Canvas tainted, using alternative method:",
          securityError,
        );
        // 如果Canvas被污染，创建一个新的Canvas来重新绘制
        const cleanCanvas = document.createElement("canvas");
        const cleanCtx = cleanCanvas.getContext("2d");

        if (cleanCtx) {
          cleanCanvas.width = finalCanvas.width;
          cleanCanvas.height = finalCanvas.height;

          // 创建一个新的Image对象来重新加载原始图片
          const newImage = new Image();
          newImage.crossOrigin = "anonymous";

          newImage.onload = () => {
            try {
              // 绘制原始图片
              cleanCtx.drawImage(newImage, 0, 0);

              // 重新应用遮罩效果（参考iOS实现，与图片编辑页面保持一致）
              const newBlackWhiteMask = createBlackWhiteMask(canvas);
              cleanCtx.globalCompositeOperation = "destination-out";

              // 创建临时画布，只保留白色区域用于清除
              const tempCanvas = document.createElement("canvas");
              const tempCtx = tempCanvas.getContext("2d");
              if (tempCtx) {
                tempCanvas.width = newBlackWhiteMask.width;
                tempCanvas.height = newBlackWhiteMask.height;

                // 绘制黑白遮罩
                tempCtx.drawImage(newBlackWhiteMask, 0, 0);

                // 只保留白色区域（涂抹区域）
                const imageData = tempCtx.getImageData(
                  0,
                  0,
                  tempCanvas.width,
                  tempCanvas.height,
                );
                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                  if (data[i] > 128) {
                    // 白色像素（涂抹区域）
                    data[i] = 255; // R
                    data[i + 1] = 255; // G
                    data[i + 2] = 255; // B
                    data[i + 3] = 255; // A
                  } else {
                    data[i] = 0; // R
                    data[i + 1] = 0; // G
                    data[i + 2] = 0; // B
                    data[i + 3] = 0; // A (透明)
                  }
                }

                tempCtx.putImageData(imageData, 0, 0);
                cleanCtx.drawImage(tempCanvas, 0, 0);
              }

              // 尝试导出
              const cleanImageData = cleanCanvas.toDataURL("image/png");
              setSourceImage(cleanImageData); // 更新显示的源图片

              // 重置状态
              setIsDrawing(false);
              setLastPoint(null);
              setMaskHistory([]);
              setHistoryIndex(-1);
              setShowInpaintModal(false);
              setCurrentEditingImage(null);

              console.log("局部重绘完成，图片已更新（使用清洁Canvas）");
            } catch (err) {
              console.error("Clean canvas export failed:", err);
              showError("图片导出失败，请重试");
            }
          };

          newImage.onerror = () => {
            console.error("Failed to reload image for clean canvas");
            showError("图片重新加载失败，请重试");
          };

          newImage.src = sourceImage!;
          return; // 异步处理，直接返回
        } else {
          throw new Error("无法创建清洁Canvas上下文");
        }
      }

      // 更新显示的源图片（带黑色标记）
      setSourceImage(finalImageData);

      // 保存遮罩图片文件（现在使用统一的遮罩生成方法）
      const maskFile = createMaskImage(canvas);
      // 这里可以保存maskFile，但目前我们使用maskCanvas状态

      // 重置绘制状态
      setIsDrawing(false);
      setLastPoint(null);
      setMaskHistory([]);
      setHistoryIndex(-1);

      // 关闭弹框
      setShowInpaintModal(false);
      setCurrentEditingImage(null);

      console.log("局部重绘完成，图片已更新");
    } catch (err) {
      console.error("Confirm inpaint error:", err);
      showError("确认修改时出错，请重试");
    }
  }, [currentEditingImage, showError, sourceImage, createMaskImage]);

  // 初始化画布
  const initializeCanvas = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      const image = imageRef.current;

      if (!canvas || !image) {
        console.warn("Canvas or image not available for initialization");
        return;
      }

      // 等待图片完全加载
      if (!image.complete || image.naturalWidth === 0) {
        console.warn("Image not fully loaded, retrying...");
        setTimeout(initializeCanvas, 100);
        return;
      }

      const ctx = canvas.getContext("2d");
      if (!ctx) {
        console.error("Failed to get canvas context");
        showError("画布初始化失败");
        return;
      }

      // 获取图片尺寸
      const imageWidth = image.naturalWidth || image.width;
      const imageHeight = image.naturalHeight || image.height;

      if (imageWidth === 0 || imageHeight === 0) {
        console.error("Invalid image dimensions:", imageWidth, imageHeight);
        showError("图片尺寸无效");
        return;
      }

      // 设置画布尺寸与图片相同（保持高分辨率）
      canvas.width = imageWidth;
      canvas.height = imageHeight;

      // 获取图片的显示尺寸（考虑object-fit: contain的影响）
      const containerRect = image.parentElement?.getBoundingClientRect();

      if (!containerRect) {
        console.error("Container not found");
        return;
      }

      // 计算图片在容器中的实际显示尺寸
      const imageAspectRatio = imageWidth / imageHeight;
      const containerAspectRatio = containerRect.width / containerRect.height;

      let displayWidth, displayHeight;

      if (imageAspectRatio > containerAspectRatio) {
        // 图片更宽，以容器宽度为准
        displayWidth = containerRect.width;
        displayHeight = containerRect.width / imageAspectRatio;
      } else {
        // 图片更高，以容器高度为准
        displayHeight = containerRect.height;
        displayWidth = containerRect.height * imageAspectRatio;
      }

      // 设置画布的CSS尺寸为显示尺寸
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 重置绘制状态
      setIsDrawing(false);
      setLastPoint(null);

      // 保存初始状态到历史记录
      const initialState = canvas.toDataURL();
      setMaskHistory([initialState]);
      setHistoryIndex(0);

      // 重置绘制状态，确保状态一致性
      setIsDrawing(false);
      setLastPoint(null);

      console.log(
        "Canvas initialized successfully:",
        canvas.width,
        "x",
        canvas.height,
      );
    } catch (err) {
      console.error("Canvas initialization error:", err);
      showError("画布初始化失败");
    }
  }, [showError]);

  // 开始绘制
  const startDrawing = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      const { x, y } = getCanvasCoordinates(e);

      setIsDrawing(true);
      setLastPoint({ x, y });

      // 使用共享的绘制工具设置
      setupDrawingTool(ctx, currentTool, brushSize);

      // 开始新的路径，但不立即绘制
      ctx.beginPath();
      ctx.moveTo(x, y);
    },
    [brushSize, currentTool, getCanvasCoordinates],
  );

  // 绘制
  const draw = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      if (!isDrawing || !lastPoint) return;

      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      const { x, y } = getCanvasCoordinates(e);

      // 计算距离，避免绘制过于密集的点
      const distance = Math.sqrt(
        Math.pow(x - lastPoint.x, 2) + Math.pow(y - lastPoint.y, 2),
      );
      if (distance < 2) return; // 如果移动距离太小，跳过绘制

      // 继续当前路径
      ctx.lineTo(x, y);
      ctx.stroke();

      // 更新最后一个点
      setLastPoint({ x, y });
    },
    [isDrawing, lastPoint, getCanvasCoordinates],
  );

  // 停止绘制
  const stopDrawing = useCallback(() => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext("2d");
      if (ctx && lastPoint) {
        // 如果只有一个点（没有移动），绘制一个小圆点
        drawDot(ctx, currentTool, lastPoint.x, lastPoint.y, brushSize);
      }
    }

    setIsDrawing(false);
    setLastPoint(null);

    // 保存当前状态到历史记录
    if (canvas) {
      const currentState = canvas.toDataURL();
      const newHistory = maskHistory.slice(0, historyIndex + 1);
      newHistory.push(currentState);
      setMaskHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  }, [isDrawing, lastPoint, brushSize, maskHistory, historyIndex, currentTool]);

  // 撤销
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);

      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext("2d");
        if (ctx) {
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = maskHistory[newIndex];
        }
      }
    }
  }, [historyIndex, maskHistory]);

  // 重做
  const handleRedo = useCallback(() => {
    if (historyIndex < maskHistory.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);

      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext("2d");
        if (ctx) {
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = maskHistory[newIndex];
        }
      }
    }
  }, [historyIndex, maskHistory]);

  // 重置画布
  const handleReset = useCallback(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        const initialState = canvas.toDataURL();
        setMaskHistory([initialState]);
        setHistoryIndex(0);
      }
    }
  }, []);

  const handleProcess = useCallback(async () => {
    if (!maskCanvas) {
      showError("请先对原图进行重绘标记需要替换的区域");
      return;
    }

    // 跳转到生成结果tab页
    setActiveTab("result");
    setIsProcessing(true);
    setTaskStatus("processing");
    resetError();

    try {
      // 准备文件
      const { originalFile, maskFile, replaceFile } = await prepareFiles();

      // 并发压缩和上传图片
      const uploadResults = await compressAndUploadImages(
        originalFile,
        maskFile,
        replaceFile,
      );

      // 提交万物替换任务
      const replaceResult = await submitReplaceTask(uploadResults);

      // 更新状态
      setTaskId(replaceResult.prompt_id);
      setTaskStatus("processing");
    } catch (error) {
      console.error("处理失败:", error);
      // 处理失败时清除任务状态，回到初始模式
      setTaskStatus(null);
      setTaskId(null);
      showError(
        error instanceof Error ? error.message : "处理失败，请稍后重试",
      );
      setActiveTab("immediate");
    } finally {
      setIsProcessing(false);
    }
  }, [
    sourceImage,
    maskCanvas,
    targetImage,
    showError,
    resetError,
    prepareFiles,
    compressAndUploadImages,
    submitReplaceTask,
  ]);

  // 素材点击处理
  const handleMaterialClick = useCallback(
    (materialUrl: string, type: ImageType) => {
      try {
        resetError();

        if (!materialUrl) {
          showError("素材图片无效");
          return;
        }

        if (type === "source") {
          setSourceImage(materialUrl);
          // 清理源图片相关的编辑状态
          if (currentEditingImage === "source") {
            setShowInpaintModal(false);
            setCurrentEditingImage(null);
          }
        } else {
          setTargetImage(materialUrl);
          // 清理目标图片相关的编辑状态
          if (currentEditingImage === "target") {
            setShowInpaintModal(false);
            setCurrentEditingImage(null);
          }
        }

        // 重置绘制状态
        setIsDrawing(false);
        setLastPoint(null);
        setMaskHistory([]);
        setHistoryIndex(-1);
      } catch (err) {
        console.error("Material click error:", err);
        showError("加载素材失败");
      }
    },
    [resetError, showError],
  );

  const canProcess = sourceImage && targetImage && !isProcessing;

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 清理错误状态
      setError(null);
      // 清理处理状态
      setIsProcessing(false);
      // 清理绘制状态
      setIsDrawing(false);
      setLastPoint(null);
    };
  }, []);

  return (
    <React.Fragment>
      <div className={styles.container}>
        {/* 头部导航 */}
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <IconButton
              icon={<BackIcon />}
              text="返回"
              onClick={() => navigate(-1)}
              className={styles.backButton}
            />
            <h1 className={styles.title}>万物替换</h1>
          </div>
          <div className={styles.headerRight}>
            <button
              className={styles.myWorksButton}
              onClick={() => navigate(Path.MyWorks)}
            >
              我的作品
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className={styles.errorMessage}>
            <span>{error}</span>
            <button onClick={resetError} className={styles.errorClose}>
              ×
            </button>
          </div>
        )}

        {/* 主要内容区域 */}
        <div className={styles.mainContainer}>
          {/* 左侧案例展示区 - 占更大宽度 */}
          <div className={styles.exampleArea}>
            {/* 案例展示 */}
            <div className={styles.exampleSection}>
              <h3>案例</h3>
              <div className={styles.exampleContainer}>
                {OBJECT_REPLACE_EXAMPLES.map((example) => (
                  <div key={example.id} className={styles.exampleItem}>
                    <div className={styles.exampleImages}>
                      <div className={styles.beforeAfter}>
                        <div className={styles.imageContainer}>
                          <img src={example.beforeImage} alt="处理前" />
                          <span className={styles.imageLabel}>原图</span>
                        </div>
                        <div className={styles.imageContainer}>
                          <img src={example.afterImage} alt="处理后" />
                          <span className={styles.imageLabel}>效果</span>
                        </div>
                        <div className={styles.arrow}>→</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 素材展示 */}
            <div className={styles.materialSection}>
              <h3>素材</h3>
              <div className={styles.materialGrid}>
                {MATERIALS.map((material) => (
                  <div
                    key={material.id}
                    className={styles.materialItem}
                    onClick={() =>
                      handleMaterialClick(material.image, material.type)
                    }
                  >
                    <div className={styles.materialImageWrapper}>
                      <img src={material.image} alt="素材" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧操作区 - 相对较小 */}
          <div className={styles.operationArea}>
            <div className={styles.editSection}>
              {/* 统一的内容容器 */}
              <div className={styles.contentContainer}>
                {/* Tab导航 */}
                <div className={styles.tabNavigation}>
                  <button
                    className={`${styles.tab} ${
                      activeTab === "immediate" ? styles.activeTab : ""
                    }`}
                    onClick={() => setActiveTab("immediate")}
                  >
                    立即使用
                  </button>
                  <button
                    className={`${styles.tab} ${
                      activeTab === "result" ? styles.activeTab : ""
                    }`}
                    onClick={() => setActiveTab("result")}
                  >
                    生成结果
                  </button>
                </div>

                {/* Tab内容 */}
                <div className={styles.tabContent}>
                  {activeTab === "immediate" && (
                    <div className={styles.immediateTab}>
                      {/* 源图上传区域 */}
                      <div className={styles.uploadSection}>
                        <label className={styles.uploadLabel}>
                          上传源图<span className={styles.required}>*</span>
                        </label>
                        <div
                          className={styles.uploadArea}
                          onClick={
                            sourceImage
                              ? undefined
                              : () => sourceFileInputRef.current?.click()
                          }
                        >
                          {sourceImage ? (
                            <div className={styles.imagePreview}>
                              <img src={sourceImage} alt="源图片" />
                              <div className={styles.imageControls}>
                                <button
                                  className={`${styles.controlButton} ${styles.inpaintButton}`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleOpenInpaint();
                                  }}
                                  title="局部重绘"
                                >
                                  <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <path d="m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08" />
                                    <path d="M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08-2 2.74-2 4-2Z" />
                                    <path d="M14 6 8.5 11.5" />
                                    <path d="M10.5 9 15 4.5" />
                                  </svg>
                                  局部重绘
                                </button>
                                <button
                                  className={`${styles.controlButton} ${styles.deleteButton}`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeImage("source");
                                  }}
                                  title="删除图片"
                                >
                                  <CloseIcon />
                                </button>
                              </div>
                            </div>
                          ) : (
                            <div className={styles.uploadPlaceholder}>
                              <div className={styles.uploadIcon}>
                                <UploadIcon />
                              </div>
                              <div className={styles.uploadText}>
                                拖拽文件到这里或点击上传
                              </div>
                            </div>
                          )}
                        </div>
                        <input
                          ref={sourceFileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleSourceUpload}
                          style={{ display: "none" }}
                        />
                      </div>

                      {/* 替换图上传区域 */}
                      <div className={styles.uploadSection}>
                        <label className={styles.uploadLabel}>
                          上传替换图<span className={styles.required}>*</span>
                        </label>
                        <div
                          className={styles.uploadArea}
                          onClick={
                            targetImage
                              ? undefined
                              : () => targetFileInputRef.current?.click()
                          }
                        >
                          {targetImage ? (
                            <div className={styles.imagePreview}>
                              <img src={targetImage} alt="目标图片" />
                              <div className={styles.imageControls}>
                                <div></div>{" "}
                                {/* 占位元素，保持右侧删除按钮位置 */}
                                <button
                                  className={`${styles.controlButton} ${styles.deleteButton}`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeImage("target");
                                  }}
                                  title="删除图片"
                                >
                                  <CloseIcon />
                                </button>
                              </div>
                            </div>
                          ) : (
                            <div className={styles.uploadPlaceholder}>
                              <div className={styles.uploadIcon}>
                                <UploadIcon />
                              </div>
                              <div className={styles.uploadText}>
                                拖拽文件到这里或点击上传
                              </div>
                            </div>
                          )}
                        </div>
                        <input
                          ref={targetFileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleTargetUpload}
                          style={{ display: "none" }}
                        />
                      </div>

                      {/* 处理按钮 */}
                      <button
                        className={`${styles.processButton} ${
                          canProcess ? styles.enabled : styles.disabled
                        }`}
                        onClick={handleProcess}
                        disabled={!canProcess}
                      >
                        {isProcessing ? "处理中..." : "Go"}
                      </button>

                      {/* 底部信息 */}
                      <div className={styles.bottomInfo}>
                        <div className={styles.credits}>
                          <span className={styles.creditsCost}>
                            消耗50创想值
                          </span>
                          <span className={styles.creditsDivider}>•</span>
                          <span
                            className={styles.creditsLink}
                            onClick={() => navigate(Path.Recharge)}
                          >
                            获取更多创想值
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === "result" && (
                    <div className={styles.resultTab}>
                      {/* 处理中状态 */}
                      {isProcessing && !taskId && (
                        <div className={styles.statusContainer}>
                          <div className={styles.processingCard}>
                            <div className={styles.statusRow}>
                              <div className={styles.statusLeft}>
                                <div className={styles.spinner}></div>
                                <span className={styles.statusLabel}>
                                  处理中
                                </span>
                              </div>
                              <span className={styles.timeLabel}>
                                {new Date().toLocaleTimeString("zh-CN", {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                })}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 任务已提交状态 */}
                      {taskId && (
                        <div className={styles.statusContainer}>
                          <div className={styles.successCard}>
                            <div className={styles.successRow}>
                              <div className={styles.checkIcon}>✓</div>
                              <div className={styles.successText}>
                                <div className={styles.successTitle}>
                                  任务已提交
                                </div>
                                <div className={styles.successDesc}>
                                  可前往“我的作品”页面查看结果
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 空状态 */}
                      {!isProcessing && !taskStatus && (
                        <div className={styles.emptyResult}>
                          <div className={styles.emptyIcon}>🎨</div>
                          <h3>暂无任务</h3>
                          <p>点击&ldquo;Go&rdquo;按钮开始万物替换</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 局部重绘弹框 */}
      {showInpaintModal && currentEditingImage && (
        <div className={styles.inpaintModal}>
          <div className={styles.inpaintModalContent}>
            {/* 关闭按钮 */}
            <div className={styles.inpaintHeader}>
              <button
                className={styles.inpaintCloseButton}
                onClick={handleCloseInpaint}
              >
                <CloseIcon />
              </button>
            </div>

            {/* 图片显示区域 */}
            <div className={styles.inpaintBody}>
              <div className={styles.inpaintCanvas}>
                <div className={styles.canvasContainer}>
                  <img
                    ref={imageRef}
                    src={
                      currentEditingImage === "source"
                        ? sourceImage!
                        : targetImage!
                    }
                    alt="编辑图片"
                    crossOrigin="anonymous"
                    onLoad={initializeCanvas}
                  />
                  <canvas
                    ref={canvasRef}
                    onMouseDown={startDrawing}
                    onMouseMove={draw}
                    onMouseUp={stopDrawing}
                    onMouseLeave={stopDrawing}
                    onTouchStart={(e) => {
                      e.preventDefault();
                      const touch = e.touches[0];
                      const mouseEvent = new MouseEvent("mousedown", {
                        clientX: touch.clientX,
                        clientY: touch.clientY,
                      });
                      startDrawing(mouseEvent as any);
                    }}
                    onTouchMove={(e) => {
                      e.preventDefault();
                      const touch = e.touches[0];
                      const mouseEvent = new MouseEvent("mousemove", {
                        clientX: touch.clientX,
                        clientY: touch.clientY,
                      });
                      draw(mouseEvent as any);
                    }}
                    onTouchEnd={(e) => {
                      e.preventDefault();
                      stopDrawing();
                    }}
                  />
                </div>
              </div>

              {/* 底部工具栏 */}
              <div className={styles.inpaintToolbar}>
                <div className={styles.toolbarLeft}>
                  {/* 画笔工具切换 */}
                  <div className={styles.toolButtons}>
                    <button
                      className={`${styles.toolButton} ${
                        currentTool === "brush" ? styles.active : ""
                      }`}
                      onClick={() => setCurrentTool("brush")}
                      title="画笔"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M12 19l7-7 3 3-7 7-3-3z" />
                        <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z" />
                        <path d="M2 2l7.586 7.586" />
                        <circle cx="11" cy="11" r="2" />
                      </svg>
                    </button>
                    <button
                      className={`${styles.toolButton} ${
                        currentTool === "eraser" ? styles.active : ""
                      }`}
                      onClick={() => setCurrentTool("eraser")}
                      title="橡皮擦"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M7 21h10" />
                        <path d="M5 21h1.5a2 2 0 0 0 1.4-.6l7.5-7.5a2 2 0 0 0 0-2.8L13.8 8.5a2 2 0 0 0-2.8 0l-7.5 7.5a2 2 0 0 0-.6 1.4V19a2 2 0 0 0 2 2Z" />
                      </svg>
                    </button>
                  </div>

                  {/* 画笔大小控制 */}
                  <div className={styles.brushSizeControl}>
                    <input
                      type="range"
                      min="5"
                      max="100"
                      value={brushSize}
                      onChange={(e) => setBrushSize(Number(e.target.value))}
                      className={styles.sizeSlider}
                    />
                    <div className={styles.sizeDisplay}>{brushSize}</div>
                  </div>
                </div>

                <div className={styles.toolbarRight}>
                  {/* 操作按钮 */}
                  <div className={styles.toolButtons}>
                    {/* 上一步 */}
                    <button
                      className={styles.toolButton}
                      onClick={handleUndo}
                      disabled={historyIndex <= 0}
                      title="上一步"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M3 7v6h6" />
                        <path d="M21 17a9 9 0 00-9-9 9 9 0 00-6 2.3L3 13" />
                      </svg>
                    </button>

                    {/* 下一步 */}
                    <button
                      className={styles.toolButton}
                      onClick={handleRedo}
                      disabled={historyIndex >= maskHistory.length - 1}
                      title="下一步"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M21 7v6h-6" />
                        <path d="M3 17a9 9 0 019-9 9 9 0 016 2.3L21 13" />
                      </svg>
                    </button>

                    {/* 重置 */}
                    <button
                      className={styles.toolButton}
                      onClick={handleReset}
                      title="重置"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M1 4v6h6" />
                        <path d="M3.51 15a9 9 0 102.13-9.36L1 10" />
                      </svg>
                    </button>
                  </div>

                  {/* 确认按钮 */}
                  <button
                    className={styles.confirmButton}
                    onClick={handleConfirmInpaint}
                  >
                    确认
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </React.Fragment>
  );
}
